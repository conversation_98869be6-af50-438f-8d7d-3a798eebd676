# 系统模式文档

## 系统架构设计
### 整体架构
```
前端层 (Vue3)
    ↓
API网关层 (Django REST)
    ↓
业务逻辑层 (Django Apps)
    ↓
数据访问层 (Django ORM)
    ↓
数据存储层 (MySQL + Redis)
```

### 模块架构
```
backend/apps/
├── users/          # 用户管理模块
├── roles/          # 角色管理模块
├── permissions/    # 权限管理模块
├── menus/          # 菜单管理模块
├── organizations/  # 组织管理模块
├── audit/          # 审计日志模块
├── statistics/     # 统计分析模块
└── core/           # 核心公共模块
```

## 关键技术决策
1. **认证方式**：JWT Token + Session混合认证
2. **权限验证**：装饰器 + 中间件双重验证
3. **数据加密**：SM4存储加密 + SM2传输加密
4. **缓存策略**：Redis缓存用户权限和菜单数据
5. **日志记录**：AOP切面记录操作日志

## 采用的设计模式
### 1. 软删除模式
```python
class SoftDeleteModel(models.Model):
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        abstract = True
```

### 2. 关系表模式
```python
class UserRole(SoftDeleteModel):
    user = models.ForeignKey(User)
    role = models.ForeignKey(Role)
    created_at = models.DateTimeField(auto_now_add=True)
```

### 3. 权限装饰器模式
```python
@permission_required('user.view')
def user_list(request):
    pass
```

### 4. 审计日志模式
```python
@audit_log(action='CREATE', resource='USER')
def create_user(request):
    pass
```

## 组件交互关系
### 用户认证流程
1. 用户登录 → 验证密码 → 生成Token → 缓存权限
2. 请求验证 → Token验证 → 权限检查 → 业务处理

### 权限验证流程
1. 请求到达 → 中间件拦截 → 解析Token → 获取用户权限
2. 权限匹配 → 允许访问 → 记录日志

### 菜单生成流程
1. 用户登录 → 获取角色 → 查询权限 → 生成菜单树
2. 缓存菜单 → 返回前端 → 动态渲染

## 核心实现路径
### 数据模型设计
1. 用户模型：自定义User模型，包含安全字段
2. 角色模型：支持层级结构的角色定义
3. 权限模型：细粒度的权限定义
4. 关系模型：用户-角色、角色-权限关系表

### API设计规范
1. RESTful API设计
2. 统一响应格式
3. 错误码标准化
4. 接口版本控制

### 前端组件设计
1. 权限控制组件
2. 菜单渲染组件
3. 表单验证组件
4. 数据表格组件
