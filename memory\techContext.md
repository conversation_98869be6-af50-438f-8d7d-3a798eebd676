# 技术上下文文档

## 使用的技术栈
### 前端技术栈
- **框架**：Vue 3
- **构建工具**：Vite
- **路由**：Vue Router 4
- **状态管理**：Pinia
- **UI组件库**：Element Plus
- **HTTP客户端**：Axios
- **加密库**：SM-Crypto (SM2/SM4)

### 后端技术栈
- **框架**：Django 4.x
- **API框架**：Django REST Framework
- **数据库ORM**：Django ORM
- **认证**：Django Authentication + JWT
- **加密库**：gmssl (SM2/SM4)
- **缓存**：Redis
- **任务队列**：Celery (可选)

### 数据库技术
- **主数据库**：MySQL 8.0.43
- **缓存数据库**：Redis 8.0.3
- **连接池**：Django-MySQL

## 开发环境配置
### Python环境
- Python 3.12.10
- 虚拟环境：virtualenv (rbacex)
- 包管理：pip

### Node.js环境
- Node.js 18+
- 包管理：npm/yarn

### 开发工具
- IDE：VS Code
- 终端：PowerShell
- 版本控制：Git

## 技术限制条件
1. **操作系统**：Windows 11兼容性
2. **Python版本**：3.12.10固定版本
3. **数据库**：MySQL 8.0.43特定版本
4. **加密算法**：必须使用SM2/SM4国密算法
5. **编码格式**：统一UTF-8编码

## 依赖项清单
### 后端主要依赖 (requirements.txt)
```
Django>=4.2.0
djangorestframework>=3.14.0
django-cors-headers>=4.0.0
PyMySQL>=1.0.3
redis>=4.5.0
django-redis>=5.2.0
gmssl>=3.2.1
PyJWT>=2.6.0
celery>=5.2.0
```

### 前端主要依赖 (package.json)
```json
{
  "vue": "^3.3.0",
  "vue-router": "^4.2.0",
  "pinia": "^2.1.0",
  "element-plus": "^2.3.0",
  "axios": "^1.4.0",
  "sm-crypto": "^0.3.0",
  "vite": "^4.3.0"
}
```

## 工具使用规范
### 代码规范
- **Python**：PEP 8标准
- **JavaScript**：ESLint + Prettier
- **Vue**：Vue官方风格指南

### 测试规范
- **后端测试**：Django TestCase
- **前端测试**：Vitest + Vue Test Utils
- **测试覆盖率**：>80%

### 部署规范
- **开发环境**：本地开发服务器
- **测试环境**：Docker容器化部署
- **生产环境**：Nginx + Gunicorn

## 安全配置
### 密码策略
- 最少8位
- 包含大写字母、小写字母、数字
- SM4加密存储

### 传输安全
- HTTPS协议
- SM2加密传输敏感数据
- CSRF保护

### 会话管理
- JWT Token认证
- Redis会话存储
- 自动过期机制
