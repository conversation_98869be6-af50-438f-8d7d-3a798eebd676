# 开发环境文档

## 操作系统
- Windows 11 家庭版

## 终端环境
- 默认终端：PowerShell
- 命令风格：PowerShell语法

## Python环境
- Python版本：3.12.10
- 虚拟环境管理：virtualenv
- 虚拟环境激活工具：workon
- 虚拟环境名称：rbacex（已存在）
- 激活命令：`workon rbacex`

## 数据库环境
### 开发数据库
- 类型：MySQL 8.0.43
- 连接字符串：mysql://rbacex_dev:m4itRTE7hVz4tSQLQLGJ@192.168.33.234:8043/rbacex_dev

### 正式数据库
- 类型：MySQL 8.0.43
- 连接字符串：mysql://rbacex_prod:6YNoxnshxxoahgjdpzml@192.168.33.234:8043/rbacex_prod

## Redis环境
### 开发Redis
- 版本：Redis 8.0.3
- 连接字符串：redis://:LxhgKEP5DJICDZNYNQLN@192.168.33.234:6803/2

### 正式Redis
- 版本：Redis 8.0.3
- 连接字符串：redis://:LxhgKEP5DJICDZNYNQLN@192.168.33.234:6803/12

## 端口配置
- 前端开发端口：5179
- 后端开发端口：8009

## 编码规范
- 项目编码：UTF-8
- 测试数据编码：UTF-8
