# 项目概要文档

## 项目名称
RBAC扩展系统 (rbacex)

## 项目目标
基于RBAC（基于角色的访问控制）系统的扩展系统，提供完整的用户权限管理解决方案。

## 核心需求
1. 用户管理：自定义用户模型，支持安全密码策略
2. 角色管理：基于角色的权限分配
3. 权限管理：细粒度权限控制
4. 菜单管理：动态菜单系统
5. 组织管理：数据权限控制
6. 审计日志：完整的操作审计
7. 统计分析：PV/UV统计

## 技术架构
- 前端：Vue 3
- 后端：Django
- 数据库：MySQL 8.0.43
- 缓存：Redis 8.0.3

## 项目结构
```
rbacex/
├── frontend/          # 前端项目
├── backend/           # 后端项目
│   ├── apps/         # 功能模块
│   └── 测试脚本/      # 测试脚本
├── 开发文档/          # 开发文档
└── memory/           # 项目记忆文档
```

## 核心特性
1. 软删除机制
2. 关系表管理
3. SM4密码加密
4. SM2传输加密
5. 自助注册开关
6. 图形验证码开关

## 默认用户
- 管理员用户名：area0
- 默认密码：Ram10240

## 默认角色
1. 超级管理员：包含所有权限
2. 系统管理员：用户的增删改查
3. 安全管理员：角色和权限的增删改查、启停用户
4. 审计管理员：查看系统的审计日志
